import { Injectable, Logger } from '@nestjs/common';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import {
  Symbol,
  SymbolUpsert,
  SymbolQueryFilters,
  SymbolSchema,
  SymbolUpsertSchema,
  SymbolQueryFiltersSchema,
  KiteInstrumentRaw,
  KiteInstrumentRawSchema,
} from './symbol.schema';
import { SymbolQuestDBRepository } from './symbol.questdb-repository';
import { SymbolEntity, SymbolCreateData } from './symbol.questdb-model';

/**
 * Symbol Repository
 *
 * High-level repository that provides symbol master data operations
 * with validation, transformation, and business logic. Uses the new
 * QuestDB repository pattern for database operations.
 *
 * Features:
 * - Zod schema validation for all operations
 * - Data transformation between API and database formats
 * - Business logic for symbol operations
 * - Integration with existing symbol schemas
 * - Backward compatibility with existing code
 */
@Injectable()
export class SymbolRepository {
  private readonly logger = new Logger(SymbolRepository.name);

  constructor(
    private readonly symbolQuestDBRepository: SymbolQuestDBRepository,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {}

  // ==================== DATA TRANSFORMATION METHODS ====================

  /**
   * Transform Symbol (Zod schema) to SymbolCreateData (QuestDB format)
   */
  private transformSymbolToCreateData(symbol: Symbol): SymbolCreateData {
    return {
      instrumentToken: symbol.instrumentToken,
      exchangeToken: symbol.exchangeToken,
      tradingSymbol: symbol.tradingSymbol,
      name: symbol.name,
      lastPrice: symbol.lastPrice,
      tickSize: symbol.tickSize,
      lotSize: symbol.lotSize,
      expiry: symbol.expiry ? new Date(symbol.expiry) : undefined,
      strike: symbol.strike,
      instrumentType: symbol.instrumentType,
      segment: symbol.segment,
      exchange: symbol.exchange,
      isActive: symbol.isActive,
    };
  }

  /**
   * Transform SymbolEntity (QuestDB format) to Symbol (Zod schema)
   */
  private transformEntityToSymbol(entity: SymbolEntity): Symbol {
    return {
      instrumentToken: entity.instrumentToken,
      exchangeToken: entity.exchangeToken,
      tradingSymbol: entity.tradingSymbol,
      name: entity.name,
      lastPrice: entity.lastPrice,
      tickSize: entity.tickSize,
      lotSize: entity.lotSize,
      expiry: entity.expiry?.toISOString(),
      strike: entity.strike,
      instrumentType: entity.instrumentType,
      segment: entity.segment,
      exchange: entity.exchange,
      isActive: entity.isActive,
      downloadedAt: entity.downloadedAt,
      updatedAt: entity.updatedAt,
    };
  }

  // ==================== SYMBOL OPERATIONS ====================

  /**
   * Upsert a single symbol record with validation
   */
  async upsertSymbol(symbolData: SymbolUpsert): Promise<{ success: boolean; isNew: boolean }> {
    try {
      // Validate input data
      const validatedData = SymbolUpsertSchema.parse(symbolData);

      this.logger.debug('Upserting symbol', {
        instrumentToken: validatedData.instrumentToken,
        tradingSymbol: validatedData.tradingSymbol,
      });

      // Check if symbol exists
      const existing = await this.findByInstrumentToken(validatedData.instrumentToken);
      const isNew = !existing;

      // Transform to QuestDB format and upsert
      const createData = this.transformSymbolToCreateData(validatedData);
      await this.symbolQuestDBRepository.upsertSymbol(createData);

      this.logger.debug('Symbol upserted successfully', {
        instrumentToken: validatedData.instrumentToken,
        isNew,
      });

      return { success: true, isNew };
    } catch (error) {
      this.logger.error('Failed to upsert symbol', {
        instrumentToken: symbolData.instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Batch upsert multiple symbols for high-performance bulk operations
   */
  async batchUpsertSymbols(symbols: SymbolUpsert[]): Promise<{
    totalProcessed: number;
    successCount: number;
    failureCount: number;
    processingTimeMs: number;
    errors: string[];
  }> {
    try {
      this.logger.log(`Starting batch upsert for ${symbols.length} symbols`);

      // Validate all symbols first
      const validatedSymbols = symbols.map((symbol) => SymbolUpsertSchema.parse(symbol));

      // Transform to QuestDB format
      const createDataArray = validatedSymbols.map((symbol) => this.transformSymbolToCreateData(symbol));

      // Use QuestDB repository for batch upsert
      const result = await this.symbolQuestDBRepository.batchUpsertSymbols(createDataArray);

      this.logger.log(`Batch upsert completed`, {
        totalProcessed: result.totalProcessed,
        successCount: result.successCount,
        failureCount: result.failureCount,
        processingTimeMs: result.processingTimeMs,
      });

      return result;
    } catch (error) {
      this.logger.error('Batch upsert failed', {
        symbolCount: symbols.length,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Find symbol by instrument token
   */
  async findByInstrumentToken(instrumentToken: string): Promise<Symbol | null> {
    try {
      const entity = await this.symbolQuestDBRepository.getSymbolByToken(instrumentToken);
      return entity ? this.transformEntityToSymbol(entity) : null;
    } catch (error) {
      this.logger.error('Failed to find symbol by instrument token', {
        instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Query symbols with filters
   */
  async querySymbols(filters: SymbolQueryFilters): Promise<Symbol[]> {
    try {
      // Validate filters
      const validatedFilters = SymbolQueryFiltersSchema.parse(filters);

      this.logger.debug('Querying symbols with filters', validatedFilters);

      // Use QuestDB repository to get latest symbols
      const entities = await this.symbolQuestDBRepository.getLatestSymbols({
        exchange: validatedFilters.exchange,
        segment: validatedFilters.segment,
        isActive: validatedFilters.isActive,
        limit: validatedFilters.limit,
        offset: validatedFilters.offset,
      });

      // Transform to Symbol format
      return entities.map((entity) => this.transformEntityToSymbol(entity));
    } catch (error) {
      this.logger.error('Failed to query symbols', {
        filters,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Search symbols by trading symbol or name
   */
  async searchSymbols(searchTerm: string, limit = 50, offset = 0): Promise<Symbol[]> {
    try {
      const entities = await this.symbolQuestDBRepository.searchSymbols(searchTerm, { limit, offset });
      return entities.map((entity) => this.transformEntityToSymbol(entity));
    } catch (error) {
      this.logger.error('Failed to search symbols', {
        searchTerm,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get symbols by exchange
   */
  async getSymbolsByExchange(exchange: string): Promise<Symbol[]> {
    try {
      const entities = await this.symbolQuestDBRepository.getSymbolsByExchange(exchange);
      return entities.map((entity) => this.transformEntityToSymbol(entity));
    } catch (error) {
      this.logger.error('Failed to get symbols by exchange', {
        exchange,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  /**
   * Get active symbols count
   */
  async getActiveSymbolsCount(): Promise<number> {
    try {
      return await this.symbolQuestDBRepository.getActiveSymbolsCount();
    } catch (error) {
      this.logger.error('Failed to get active symbols count', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }
}

/**
 * Symbol QuestDB Model
 *
 * Defines the QuestDB table schema and TypeScript interfaces for symbol master data.
 * This model replaces the existing schema management in libs/core/src/schemas/symbol.model.ts
 * and provides a cleaner, feature-module-specific approach to QuestDB table management.
 *
 * Features:
 * - TypeScript interfaces for type-safe operations
 * - QuestDB table schema definition
 * - Query templates for common operations
 * - Time-series optimized structure
 */

import type { QuestDBTableSchema } from '@app/common/questdb';

/**
 * QuestDB table name for symbol master data
 */
export const SYMBOL_TABLE = 'symbol';

/**
 * Symbol entity interface for QuestDB operations
 * Represents the complete symbol record structure
 */
export interface SymbolEntity {
  // Primary identifiers
  instrumentToken: string;
  exchangeToken: string;

  // Symbol information
  tradingSymbol: string;
  name: string;

  // Market data
  lastPrice: number;
  tickSize: number;
  lotSize: number;

  // Derivatives specific
  expiry?: Date;
  strike?: number;

  // Classification
  instrumentType: string;
  segment: string;
  exchange: string;

  // Status and metadata
  isActive: boolean;
  downloadedAt: string;
  updatedAt: string;
  timestamp: string; // Designated timestamp column
}

/**
 * Symbol creation data interface
 * Used for inserting new symbol records
 */
export interface SymbolCreateData {
  instrumentToken: string;
  exchangeToken: string;
  tradingSymbol: string;
  name: string;
  lastPrice: number;
  tickSize: number;
  lotSize: number;
  expiry?: Date;
  strike?: number;
  instrumentType: string;
  segment: string;
  exchange: string;
  isActive: boolean;
}

/**
 * Symbol update data interface
 * Used for updating existing symbol records
 */
export interface SymbolUpdateData {
  tradingSymbol?: string;
  name?: string;
  lastPrice?: number;
  tickSize?: number;
  lotSize?: number;
  expiry?: Date;
  strike?: number;
  instrumentType?: string;
  segment?: string;
  exchange?: string;
  isActive?: boolean;
}

/**
 * QuestDB table creation SQL for symbol master data
 */
export const CREATE_SYMBOL_TABLE_SQL = `
CREATE TABLE IF NOT EXISTS ${SYMBOL_TABLE} (
    -- Primary identifiers
    instrument_token STRING NOT NULL,
    exchange_token STRING NOT NULL,
    
    -- Symbol information
    trading_symbol STRING NOT NULL,
    name STRING,
    
    -- Market data
    last_price DOUBLE,
    tick_size DOUBLE NOT NULL,
    lot_size INT NOT NULL,
    
    -- Derivatives specific
    expiry TIMESTAMP,
    strike DOUBLE,
    
    -- Classification
    instrument_type STRING NOT NULL,
    segment STRING NOT NULL,
    exchange STRING NOT NULL,
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT true,
    downloaded_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    timestamp TIMESTAMP NOT NULL
) timestamp(timestamp) PARTITION BY DAY;
`;

/**
 * QuestDB indexes for optimized queries
 */
export const CREATE_SYMBOL_INDEXES_SQL = [
  // Primary index on instrument_token for fast lookups
  `ALTER TABLE ${SYMBOL_TABLE} ADD INDEX idx_instrument_token (instrument_token);`,

  // Index on trading_symbol for symbol searches
  `ALTER TABLE ${SYMBOL_TABLE} ADD INDEX idx_trading_symbol (trading_symbol);`,

  // Composite index for common query patterns
  `ALTER TABLE ${SYMBOL_TABLE} ADD INDEX idx_exchange_segment (exchange, segment);`,

  // Index on is_active for filtering active symbols
  `ALTER TABLE ${SYMBOL_TABLE} ADD INDEX idx_is_active (is_active);`,
];

/**
 * Common SQL query templates for symbol operations
 */
export const SYMBOL_QUERIES = {
  // Upsert symbol data with deduplication
  UPSERT_SYMBOL: `
    INSERT INTO ${SYMBOL_TABLE} (
      instrument_token, exchange_token, trading_symbol, name,
      last_price, tick_size, lot_size, expiry, strike,
      instrument_type, segment, exchange, is_active,
      downloaded_at, updated_at, timestamp
    ) VALUES (
      $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
    )
    ON CONFLICT (instrument_token, timestamp)
    DO UPDATE SET
      exchange_token = EXCLUDED.exchange_token,
      trading_symbol = EXCLUDED.trading_symbol,
      name = EXCLUDED.name,
      last_price = EXCLUDED.last_price,
      tick_size = EXCLUDED.tick_size,
      lot_size = EXCLUDED.lot_size,
      expiry = EXCLUDED.expiry,
      strike = EXCLUDED.strike,
      instrument_type = EXCLUDED.instrument_type,
      segment = EXCLUDED.segment,
      exchange = EXCLUDED.exchange,
      is_active = EXCLUDED.is_active,
      updated_at = EXCLUDED.updated_at
  `,

  // Get latest symbols by filters
  GET_LATEST_SYMBOLS: `
    SELECT * FROM ${SYMBOL_TABLE}
    WHERE timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_TABLE} s2 
      WHERE s2.instrument_token = ${SYMBOL_TABLE}.instrument_token
    )
  `,

  // Get symbol by instrument token (latest version)
  GET_SYMBOL_BY_TOKEN: `
    SELECT * FROM ${SYMBOL_TABLE}
    WHERE instrument_token = $1
    AND timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_TABLE} s2 
      WHERE s2.instrument_token = $1
    )
    LIMIT 1
  `,

  // Get active symbols count
  GET_ACTIVE_SYMBOLS_COUNT: `
    SELECT COUNT(*) as count
    FROM ${SYMBOL_TABLE}
    WHERE is_active = true
    AND timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_TABLE} s2 
      WHERE s2.instrument_token = ${SYMBOL_TABLE}.instrument_token
    )
  `,

  // Get symbols by exchange (latest versions only)
  GET_SYMBOLS_BY_EXCHANGE: `
    SELECT * FROM ${SYMBOL_TABLE}
    WHERE exchange = $1
    AND is_active = true
    AND timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_TABLE} s2 
      WHERE s2.instrument_token = ${SYMBOL_TABLE}.instrument_token
    )
    ORDER BY trading_symbol
  `,

  // Search symbols by trading symbol or name
  SEARCH_SYMBOLS: `
    SELECT * FROM ${SYMBOL_TABLE}
    WHERE (trading_symbol ILIKE $1 OR name ILIKE $1)
    AND is_active = true
    AND timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_TABLE} s2
      WHERE s2.instrument_token = ${SYMBOL_TABLE}.instrument_token
    )
    ORDER BY trading_symbol
    LIMIT $2 OFFSET $3
  `,
};

/**
 * Symbol QuestDB table schema definition
 * Used by the QuestDB service for automatic table initialization
 */
export const SYMBOL_QUESTDB_SCHEMA: QuestDBTableSchema = {
  tableName: SYMBOL_TABLE,
  createTableSQL: CREATE_SYMBOL_TABLE_SQL,
  createIndexesSQL: CREATE_SYMBOL_INDEXES_SQL,
  queries: SYMBOL_QUERIES,
  designatedTimestamp: 'timestamp',
  partitionBy: 'DAY',
};

/**
 * Field mapping between TypeScript interface and QuestDB columns
 * Used for data transformation in repository operations
 */
export const SYMBOL_FIELD_MAPPING = {
  instrumentToken: 'instrument_token',
  exchangeToken: 'exchange_token',
  tradingSymbol: 'trading_symbol',
  name: 'name',
  lastPrice: 'last_price',
  tickSize: 'tick_size',
  lotSize: 'lot_size',
  expiry: 'expiry',
  strike: 'strike',
  instrumentType: 'instrument_type',
  segment: 'segment',
  exchange: 'exchange',
  isActive: 'is_active',
  downloadedAt: 'downloaded_at',
  updatedAt: 'updated_at',
  timestamp: 'timestamp',
} as const;

/**
 * Helper function to transform TypeScript field names to QuestDB column names
 */
export function transformToQuestDBColumns(data: Record<string, unknown>): Record<string, unknown> {
  const transformed: Record<string, unknown> = {};

  for (const [key, value] of Object.entries(data)) {
    const columnName = SYMBOL_FIELD_MAPPING[key as keyof typeof SYMBOL_FIELD_MAPPING] || key;
    transformed[columnName] = value;
  }

  return transformed;
}

/**
 * Helper function to transform QuestDB columns to TypeScript field names
 */
export function transformFromQuestDBColumns(data: Record<string, unknown>): Record<string, unknown> {
  const transformed: Record<string, unknown> = {};

  // Create reverse mapping
  const reverseMapping = Object.fromEntries(Object.entries(SYMBOL_FIELD_MAPPING).map(([key, value]) => [value, key]));

  for (const [key, value] of Object.entries(data)) {
    const fieldName = reverseMapping[key] || key;
    transformed[fieldName] = value;
  }

  return transformed;
}

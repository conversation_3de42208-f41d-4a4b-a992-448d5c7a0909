/**
 * Symbol QuestDB Repository
 *
 * Extends BaseQuestDBRepository to provide symbol-specific database operations
 * for QuestDB time-series storage. Handles symbol master data with optimized
 * queries for time-series operations and Kite Connect integration.
 *
 * Features:
 * - Symbol-specific CRUD operations
 * - Time-series optimized queries
 * - Batch upsert operations for symbol master data
 * - Latest symbol data retrieval
 * - Exchange and segment filtering
 * - Symbol search capabilities
 */

import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { QuestDBService } from '@app/core/questdb';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import {
  BaseQuestDBRepository,
  QuestDBTableSchema,
  QuestDBQueryResult,
  BatchOperationResult,
  createQuestDBRepositoryError,
} from '@app/common/questdb';
import {
  SymbolEntity,
  SymbolCreateData,
  SymbolUpdateData,
  SYMBOL_QUESTDB_SCHEMA,
  SYMBOL_QUERIES,
  transformToQuestDBColumns,
  transformFromQuestDBColumns,
} from './symbol.questdb-model';

/**
 * Symbol QuestDB Repository
 *
 * Provides high-performance symbol master data operations using QuestDB
 * with time-series optimization and batch processing capabilities.
 */
@Injectable()
export class SymbolQuestDBRepository
  extends BaseQuestDBRepository<SymbolEntity, SymbolCreateData, SymbolUpdateData, string>
  implements OnModuleInit
{
  constructor(questdbService: QuestDBService, dateTimeUtils: DateTimeUtilsService, errorUtils: ErrorUtilsService) {
    super(questdbService, dateTimeUtils, errorUtils, SymbolQuestDBRepository.name, SYMBOL_QUESTDB_SCHEMA.tableName);
  }

  /**
   * Initialize the repository and register table schema
   */
  async onModuleInit(): Promise<void> {
    // Register the table schema with QuestDB service for automatic initialization
    this.questdbService.registerTableSchema(SYMBOL_QUESTDB_SCHEMA);
    this.logger.log('Symbol QuestDB repository initialized and table schema registered');
  }

  // ==================== ABSTRACT METHOD IMPLEMENTATIONS ====================

  /**
   * Get the table schema definition
   */
  protected getTableSchema(): QuestDBTableSchema {
    return SYMBOL_QUESTDB_SCHEMA;
  }

  /**
   * Transform raw database result to SymbolEntity
   */
  protected transformToEntity(raw: Record<string, unknown>): SymbolEntity {
    const transformed = transformFromQuestDBColumns(raw);

    return {
      instrumentToken: String(transformed.instrumentToken),
      exchangeToken: String(transformed.exchangeToken),
      tradingSymbol: String(transformed.tradingSymbol),
      name: String(transformed.name || ''),
      lastPrice: Number(transformed.lastPrice || 0),
      tickSize: Number(transformed.tickSize),
      lotSize: Number(transformed.lotSize),
      expiry: transformed.expiry ? new Date(transformed.expiry as string) : undefined,
      strike: transformed.strike ? Number(transformed.strike) : undefined,
      instrumentType: String(transformed.instrumentType),
      segment: String(transformed.segment),
      exchange: String(transformed.exchange),
      isActive: Boolean(transformed.isActive),
      downloadedAt: String(transformed.downloadedAt),
      updatedAt: String(transformed.updatedAt),
      timestamp: String(transformed.timestamp),
    };
  }

  /**
   * Transform SymbolCreateData to database format
   */
  protected transformCreateData(data: SymbolCreateData): Record<string, unknown> {
    const now = this.dateTimeUtils.getUtcNow();

    const createData = {
      ...data,
      downloadedAt: now,
      updatedAt: now,
      timestamp: now,
    };

    return transformToQuestDBColumns(createData);
  }

  /**
   * Transform SymbolUpdateData to database format
   */
  protected transformUpdateData(data: SymbolUpdateData): Record<string, unknown> {
    const updateData = {
      ...data,
      updatedAt: this.dateTimeUtils.getUtcNow(),
    };

    return transformToQuestDBColumns(updateData);
  }

  // ==================== SYMBOL-SPECIFIC OPERATIONS ====================

  /**
   * Upsert symbol data with conflict resolution on instrument_token
   */
  async upsertSymbol(data: SymbolCreateData): Promise<SymbolEntity> {
    return this.executeWithErrorHandling('upsertSymbol', async () => {
      this.logOperation('upsertSymbol', { instrumentToken: data.instrumentToken });

      const transformedData = this.transformCreateData(data);
      const values = Object.values(transformedData);

      const result = await this.executeQuery<Record<string, unknown>>(SYMBOL_QUERIES.UPSERT_SYMBOL, values);

      if (result.rowCount === 0) {
        throw createQuestDBRepositoryError.invalidData('Upsert operation returned no rows');
      }

      this.logger.log(`Successfully upserted symbol: ${data.instrumentToken}`);
      return this.transformToEntity(result.data[0]);
    });
  }

  /**
   * Batch upsert multiple symbols
   */
  async batchUpsertSymbols(symbols: SymbolCreateData[]): Promise<BatchOperationResult> {
    return this.executeWithErrorHandling('batchUpsertSymbols', async () => {
      this.logOperation('batchUpsertSymbols', { symbolCount: symbols.length });

      const startTime = this.dateTimeUtils.getTime();
      let successCount = 0;
      let failureCount = 0;
      const errors: string[] = [];

      // Process symbols in batches of 1000
      const batchSize = 1000;
      for (let i = 0; i < symbols.length; i += batchSize) {
        const batch = symbols.slice(i, i + batchSize);

        try {
          for (const symbol of batch) {
            await this.upsertSymbol(symbol);
            successCount++;
          }
        } catch (error) {
          failureCount += batch.length;
          const errorMessage = this.errorUtils.getErrorMessage(error);
          errors.push(`Batch ${Math.floor(i / batchSize) + 1}: ${errorMessage}`);

          this.logger.warn(`Batch upsert failed for batch starting at index ${i}`, {
            error: errorMessage,
            batchSize: batch.length,
          });
        }
      }

      const processingTimeMs = this.dateTimeUtils.getTime() - startTime;

      this.logger.log(`Batch upsert completed`, {
        totalProcessed: symbols.length,
        successCount,
        failureCount,
        processingTimeMs,
      });

      return {
        totalProcessed: symbols.length,
        successCount,
        failureCount,
        processingTimeMs,
        errors,
      };
    });
  }

  /**
   * Get latest symbols (most recent version of each instrument)
   */
  async getLatestSymbols(filters?: {
    exchange?: string;
    segment?: string;
    isActive?: boolean;
    limit?: number;
    offset?: number;
  }): Promise<SymbolEntity[]> {
    return this.executeWithErrorHandling('getLatestSymbols', async () => {
      this.logOperation('getLatestSymbols', { filters });

      let query = SYMBOL_QUERIES.GET_LATEST_SYMBOLS;
      const params: unknown[] = [];
      let paramIndex = 1;

      // Add filters
      const whereConditions: string[] = [];

      if (filters?.exchange) {
        whereConditions.push(`exchange = $${paramIndex}`);
        params.push(filters.exchange);
        paramIndex++;
      }

      if (filters?.segment) {
        whereConditions.push(`segment = $${paramIndex}`);
        params.push(filters.segment);
        paramIndex++;
      }

      if (filters?.isActive !== undefined) {
        whereConditions.push(`is_active = $${paramIndex}`);
        params.push(filters.isActive);
        paramIndex++;
      }

      if (whereConditions.length > 0) {
        query += ` AND ${whereConditions.join(' AND ')}`;
      }

      query += ` ORDER BY trading_symbol`;

      // Add pagination
      if (filters?.limit) {
        query += ` LIMIT $${paramIndex}`;
        params.push(filters.limit);
        paramIndex++;
      }

      if (filters?.offset) {
        query += ` OFFSET $${paramIndex}`;
        params.push(filters.offset);
      }

      const result = await this.executeQuery<Record<string, unknown>>(query, params);
      return result.data.map((row) => this.transformToEntity(row));
    });
  }

  /**
   * Get symbol by instrument token (latest version)
   */
  async getSymbolByToken(instrumentToken: string): Promise<SymbolEntity | null> {
    return this.executeWithErrorHandling('getSymbolByToken', async () => {
      this.logOperation('getSymbolByToken', { instrumentToken });

      const result = await this.executeQuery<Record<string, unknown>>(SYMBOL_QUERIES.GET_SYMBOL_BY_TOKEN, [
        instrumentToken,
      ]);

      if (result.rowCount === 0) {
        return null;
      }

      return this.transformToEntity(result.data[0]);
    });
  }

  /**
   * Search symbols by trading symbol or name
   */
  async searchSymbols(
    searchTerm: string,
    options?: {
      limit?: number;
      offset?: number;
    },
  ): Promise<SymbolEntity[]> {
    return this.executeWithErrorHandling('searchSymbols', async () => {
      this.logOperation('searchSymbols', { searchTerm, options });

      const searchPattern = `%${searchTerm}%`;
      const limit = options?.limit || 50;
      const offset = options?.offset || 0;

      const result = await this.executeQuery<Record<string, unknown>>(SYMBOL_QUERIES.SEARCH_SYMBOLS, [
        searchPattern,
        limit,
        offset,
      ]);

      return result.data.map((row) => this.transformToEntity(row));
    });
  }

  /**
   * Get active symbols count
   */
  async getActiveSymbolsCount(): Promise<number> {
    return this.executeWithErrorHandling('getActiveSymbolsCount', async () => {
      const result = await this.executeQuery<{ count: number }>(SYMBOL_QUERIES.GET_ACTIVE_SYMBOLS_COUNT);
      return Number(result.data[0].count);
    });
  }

  /**
   * Get symbols by exchange (latest versions only)
   */
  async getSymbolsByExchange(exchange: string): Promise<SymbolEntity[]> {
    return this.executeWithErrorHandling('getSymbolsByExchange', async () => {
      this.logOperation('getSymbolsByExchange', { exchange });

      const result = await this.executeQuery<Record<string, unknown>>(SYMBOL_QUERIES.GET_SYMBOLS_BY_EXCHANGE, [
        exchange,
      ]);

      return result.data.map((row) => this.transformToEntity(row));
    });
  }
}

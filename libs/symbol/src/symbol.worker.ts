import { Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import {} from '@nestjs/bullmq';
import { BaseWorkerService, QueueHealthService, QueueNameEnum, type JobResult } from '@app/core/queue';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { EnvService } from '@app/core/env';
import { createQueueConnection, DEFAULT_WORKER_OPTIONS } from '@app/core/queue/queue.config';
import { SymbolService } from './symbol.service';
import { SymbolDownloadResult } from './symbol.schema';
import type { SymbolDownloadJobDataType } from './symbol.queue';

/**
 * Symbol download worker
 * Processes symbol  data download jobs with Kite Connect integration
 *
 * Features:
 * - Real Kite Connect API integration
 * - QuestDB repository for high-performance storage
 * - Comprehensive audit logging
 * - Progress tracking and error handling
 * - Batch processing with configurable sizes
 */
@Injectable()
export class SymbolDownloadWorkerService extends BaseWorkerService<SymbolDownloadJobDataType> {
  constructor(
    private readonly queueHealthService: QueueHealthService,
    private readonly symbolService: SymbolService,
    private readonly envService: EnvService,
    private readonly errorUtils: ErrorUtilsService,
    protected readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    const connectionConfig = createQueueConnection(envService);
    super(
      QueueNameEnum.enum.SYMBOL_DOWNLOAD,
      connectionConfig,
      {
        ...DEFAULT_WORKER_OPTIONS,
        concurrency: 1, // Process 1 job at a time for symbol downloads
      },
      dateTimeUtils,
    );

    // Register health check
    this.queueHealthService.registerWorkerHealthCheck(SymbolDownloadWorkerService.name, () => this.getHealthStatus());
  }

  /**
   * Process symbol download job with Kite Connect integration
   */
  protected async processJob(job: Job<SymbolDownloadJobDataType>): Promise<JobResult<SymbolDownloadResult>> {
    const startTime = this.dateTimeUtils.getTime();
    const startedAt = this.dateTimeUtils.getUtcNow();
    const { exchange, segment, forceRefresh, batchSize, requestId } = job.data;

    this.logger.log(`Processing symbol download job for ${exchange}:${segment}`, {
      jobId: job.id,
      exchange,
      segment,
      forceRefresh,
      batchSize,
      requestId,
    });

    // Log audit event for job start
    this.logger.log('Symbol download job started', {
      jobId: job.id,
      exchange,
      segment,
      requestId,
      forceRefresh,
      batchSize,
    });

    try {
      // Update progress to 10%
      await job.updateProgress(10);

      // Use the enhanced symbol service for download and storage
      const downloadResult = await this.symbolService.downloadAndStoreSymbol(
        exchange === 'ALL' ? undefined : exchange,
        segment === 'ALL' ? undefined : segment,
        forceRefresh,
      );

      // Update progress to 90%
      await job.updateProgress(90);

      // Log successful completion
      this.logger.log('Symbol download completed successfully', {
        jobId: job.id,
        requestId,
        result: downloadResult,
      });

      // Update progress to 100%
      await job.updateProgress(100);

      const totalDuration = this.dateTimeUtils.getTime() - startTime;

      this.logger.log(`Symbol download completed for ${exchange}:${segment}`, {
        jobId: job.id,
        ...downloadResult,
        totalDuration,
      });

      return {
        success: true,
        data: downloadResult,
        jobId: job.id || 'unknown',
        queueName: this.queueName,
        processedAt: this.dateTimeUtils.getUtcNow(),
        duration: totalDuration,
        attempts: job.attemptsMade,
      };
    } catch (error) {
      const errorMessage = this.errorUtils.getErrorMessage(error);
      const completedAt = this.dateTimeUtils.getUtcNow();
      const duration = this.dateTimeUtils.getTime() - startTime;

      this.logger.error(`Symbol download failed for ${exchange}:${segment}`, {
        jobId: job.id,
        error: errorMessage,
        exchange,
        segment,
        duration,
      });

      // Log failure
      this.logger.error('Symbol download failed', {
        jobId: job.id,
        exchange,
        segment,
        requestId,
        error: errorMessage,
        duration,
      });

      throw error; // Re-throw to let BullMQ handle retries
    }
  }

  /**
   * Handle job completion
   */
  protected onJobCompleted(job: Job<SymbolDownloadJobDataType>, result: JobResult<unknown>): void {
    this.logger.log(`Symbol download job completed successfully`, {
      jobId: job.id,
      exchange: job.data.exchange,
      segment: job.data.segment,
      result,
    });
  }

  /**
   * Handle job failure
   */
  protected onJobFailed(job: Job<SymbolDownloadJobDataType> | undefined, error: Error): void {
    if (job) {
      this.logger.error(`Symbol download job failed`, {
        jobId: job.id,
        exchange: job.data.exchange,
        segment: job.data.segment,
        error: error.message,
        attempts: job.attemptsMade,
      });
    } else {
      this.logger.error(`Symbol download job failed (job undefined)`, {
        error: error.message,
      });
    }
  }

  /**
   * Handle job progress updates
   */
  protected onJobProgress(job: Job<SymbolDownloadJobDataType>, progress: number | object | string): void {
    this.logger.debug(`Symbol download job progress`, {
      jobId: job.id,
      exchange: job.data.exchange,
      segment: job.data.segment,
      progress,
    });
  }

  /**
   * Handle stalled jobs
   */
  protected onJobStalled(jobId: string): void {
    this.logger.warn(`Symbol download job stalled`, { jobId });
  }

  /**
   * Handle worker errors
   */
  protected onWorkerError(error: Error): void {
    this.logger.error(`Symbol download worker error`, { error: error.message });
  }

  /**
   * Cleanup on module destroy
   */
  async onModuleDestroy(): Promise<void> {
    // Unregister health check
    this.queueHealthService.unregisterWorkerHealthCheck(SymbolDownloadWorkerService.name);

    // Call parent cleanup
    await super.onModuleDestroy();
  }
}

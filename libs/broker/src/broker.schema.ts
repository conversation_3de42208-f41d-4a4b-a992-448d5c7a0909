import { z } from 'zod/v4';
import {
  baseUpdatableEntitySchema,
  nameSchema,
  optionalUtcDateTimeSchema,
  positiveIntSchema,
  nonNegativeIntSchema,
} from '@app/common/schema';
import { BrokerTypeEnum, BrokerStatusEnum, BrokerConnectionStatusEnum, BROKER_VALIDATION } from './broker.constants';

// ==================== BROKER VALIDATION SCHEMAS ====================

/**
 * API Key validation schema for Zerodha Kite
 */
export const apiKeySchema = z
  .string()
  .length(BROKER_VALIDATION.API_KEY_LENGTH, `API key must be exactly ${BROKER_VALIDATION.API_KEY_LENGTH} characters`)
  .regex(BROKER_VALIDATION.API_KEY_PATTERN, 'API key must contain only lowercase letters and numbers')
  .describe('Broker API key for authentication');

/**
 * API Secret validation schema for Zerodha Kite
 */
export const apiSecretSchema = z
  .string()
  .length(
    BROKER_VALIDATION.API_SECRET_LENGTH,
    `API secret must be exactly ${BROKER_VALIDATION.API_SECRET_LENGTH} characters`,
  )
  .regex(BROKER_VALIDATION.API_SECRET_PATTERN, 'API secret must contain only lowercase letters and numbers')
  .describe('Broker API secret for authentication');

/**
 * Access Token validation schema
 */
export const accessTokenSchema = z
  .string()
  .min(
    BROKER_VALIDATION.ACCESS_TOKEN_MIN_LENGTH,
    `Access token must be at least ${BROKER_VALIDATION.ACCESS_TOKEN_MIN_LENGTH} characters`,
  )
  .max(
    BROKER_VALIDATION.ACCESS_TOKEN_MAX_LENGTH,
    `Access token cannot exceed ${BROKER_VALIDATION.ACCESS_TOKEN_MAX_LENGTH} characters`,
  )
  .regex(BROKER_VALIDATION.ACCESS_TOKEN_PATTERN, 'Access token contains invalid characters')
  .describe('Broker access token for API calls');

/**
 * Optional access token schema
 */
export const optionalAccessTokenSchema = accessTokenSchema.optional();

/**
 * User ID schema for broker association
 */
export const userIdSchema = z
  .string()
  .min(1, 'User ID is required')
  .max(255, 'User ID cannot exceed 255 characters')
  .describe('User ID associated with broker credentials');

/**
 * Broker settings schema for configuration
 */
export const brokerSettingsSchema = z
  .object({
    autoReconnect: z.boolean().default(true).describe('Enable automatic reconnection'),
    maxRetryAttempts: nonNegativeIntSchema.max(10).default(3).describe('Maximum retry attempts'),
    retryDelay: positiveIntSchema.max(60000).default(1000).describe('Retry delay in milliseconds'),
    subscriptionMode: z.enum(['ltp', 'quote', 'full']).default('quote').describe('WebSocket subscription mode'),
    enableOrderUpdates: z.boolean().default(true).describe('Enable order update notifications'),
    enablePositionUpdates: z.boolean().default(true).describe('Enable position update notifications'),
  })
  .strict()
  .describe('Broker-specific configuration settings');

// ==================== CORE BROKER SCHEMAS ====================

/**
 * Complete Broker entity schema with all fields
 * Extends base updatable entity with audit fields
 */
export const BrokerSchema = z.object({
  ...baseUpdatableEntitySchema.shape,
  name: nameSchema
    .min(BROKER_VALIDATION.NAME_MIN_LENGTH)
    .max(BROKER_VALIDATION.NAME_MAX_LENGTH)
    .describe('Human-readable name for the broker configuration'),
  type: BrokerTypeEnum.describe('Type of broker (e.g., ZERODHA_KITE)'),
  userId: userIdSchema.describe('ID of the user who owns this broker configuration'),
  apiKey: apiKeySchema.describe('Broker API key for authentication'),
  apiSecret: apiSecretSchema.describe('Broker API secret for authentication'),
  accessToken: optionalAccessTokenSchema.describe('Broker access token (obtained after login)'),
  status: BrokerStatusEnum.default('INACTIVE').describe('Current status of broker credentials'),
  isActive: z.boolean().default(true).describe('Whether broker configuration is active'),
  lastConnectedAt: optionalUtcDateTimeSchema.describe('Last successful connection timestamp'),
  lastErrorMessage: z.string().max(1000).optional().describe('Last error message from broker'),
  connectionAttempts: nonNegativeIntSchema.default(0).describe('Number of connection attempts'),
  settings: brokerSettingsSchema.optional().describe('Broker-specific configuration settings'),
});

/**
 * Schema for creating a new broker configuration
 * Excludes system-generated fields
 */
export const CreateBrokerSchema = z
  .object({
    name: nameSchema
      .min(BROKER_VALIDATION.NAME_MIN_LENGTH)
      .max(BROKER_VALIDATION.NAME_MAX_LENGTH)
      .describe('Human-readable name for the broker configuration'),
    type: BrokerTypeEnum.describe('Type of broker (e.g., ZERODHA_KITE)'),
    userId: userIdSchema.describe('ID of the user who owns this broker configuration'),
    apiKey: apiKeySchema.describe('Broker API key for authentication'),
    apiSecret: apiSecretSchema.describe('Broker API secret for authentication'),
    accessToken: optionalAccessTokenSchema.describe('Broker access token (optional during creation)'),
    settings: brokerSettingsSchema.optional().describe('Broker-specific configuration settings'),
  })
  .strict();

/**
 * Schema for updating an existing broker configuration
 * All fields are optional except those that shouldn't change
 */
export const UpdateBrokerSchema = z
  .object({
    name: nameSchema
      .min(BROKER_VALIDATION.NAME_MIN_LENGTH)
      .max(BROKER_VALIDATION.NAME_MAX_LENGTH)
      .optional()
      .describe('Human-readable name for the broker configuration'),
    apiKey: apiKeySchema.optional().describe('Updated broker API key'),
    apiSecret: apiSecretSchema.optional().describe('Updated broker API secret'),
    accessToken: optionalAccessTokenSchema.describe('Updated broker access token'),
    status: BrokerStatusEnum.optional().describe('Updated broker status'),
    isActive: z.boolean().optional().describe('Whether broker configuration is active'),
    lastErrorMessage: z.string().max(1000).optional().describe('Last error message from broker'),
    settings: brokerSettingsSchema.optional().describe('Updated broker-specific settings'),
  })
  .strict();

/**
 * Schema for broker search and filtering
 */
export const BrokerSearchSchema = z
  .object({
    userId: userIdSchema.optional().describe('Filter by user ID'),
    type: BrokerTypeEnum.optional().describe('Filter by broker type'),
    status: BrokerStatusEnum.optional().describe('Filter by broker status'),
    isActive: z.boolean().optional().describe('Filter by active status'),
    hasAccessToken: z.boolean().optional().describe('Filter by presence of access token'),
    name: z.string().min(1).max(100).optional().describe('Filter by broker name (partial match)'),
  })
  .strict();

// ==================== RESPONSE SCHEMAS ====================

/**
 * Public broker schema (excludes sensitive fields)
 * Used for API responses to avoid exposing credentials
 */
export const PublicBrokerSchema = z.object({
  ...baseUpdatableEntitySchema.shape,
  name: nameSchema.describe('Human-readable name for the broker configuration'),
  type: BrokerTypeEnum.describe('Type of broker'),
  userId: userIdSchema.describe('ID of the user who owns this broker configuration'),
  status: BrokerStatusEnum.describe('Current status of broker credentials'),
  isActive: z.boolean().describe('Whether broker configuration is active'),
  lastConnectedAt: optionalUtcDateTimeSchema.describe('Last successful connection timestamp'),
  lastErrorMessage: z.string().optional().describe('Last error message from broker'),
  connectionAttempts: nonNegativeIntSchema.describe('Number of connection attempts'),
  hasAccessToken: z.boolean().describe('Whether broker has a valid access token'),
  settings: brokerSettingsSchema.optional().describe('Broker-specific configuration settings'),
});

/**
 * Broker connection status schema for real-time updates
 */
export const BrokerConnectionSchema = z.object({
  brokerId: positiveIntSchema.describe('Broker configuration ID'),
  status: BrokerConnectionStatusEnum.describe('Current connection status'),
  lastUpdated: optionalUtcDateTimeSchema.describe('Last status update timestamp'),
  errorMessage: z.string().optional().describe('Error message if connection failed'),
});

// ==================== OPERATION SCHEMAS ====================

/**
 * Schema for broker credential validation
 */
export const ValidateBrokerCredentialsSchema = z
  .object({
    type: BrokerTypeEnum.describe('Type of broker to validate'),
    apiKey: apiKeySchema.describe('API key to validate'),
    apiSecret: apiSecretSchema.describe('API secret to validate'),
    accessToken: optionalAccessTokenSchema.describe('Access token to validate (optional)'),
  })
  .strict();

/**
 * Schema for broker authentication result
 */
export const BrokerAuthResultSchema = z.object({
  success: z.boolean().describe('Whether authentication was successful'),
  accessToken: accessTokenSchema.optional().describe('Generated access token if successful'),
  expiresAt: optionalUtcDateTimeSchema.describe('Token expiration timestamp'),
  errorMessage: z.string().optional().describe('Error message if authentication failed'),
  userProfile: z.record(z.unknown()).optional().describe('User profile data from broker'),
});

// ==================== TYPE EXPORTS ====================
// Following PatternTrade API standards: Export TypeScript types from Zod schemas

export type Broker = z.output<typeof BrokerSchema>;
export type CreateBroker = z.output<typeof CreateBrokerSchema>;
export type UpdateBroker = z.output<typeof UpdateBrokerSchema>;
export type BrokerSearch = z.output<typeof BrokerSearchSchema>;
export type PublicBroker = z.output<typeof PublicBrokerSchema>;
export type BrokerConnection = z.output<typeof BrokerConnectionSchema>;
export type ValidateBrokerCredentials = z.output<typeof ValidateBrokerCredentialsSchema>;
export type BrokerAuthResult = z.output<typeof BrokerAuthResultSchema>;
export type BrokerSettings = z.output<typeof brokerSettingsSchema>;

// ==================== SCHEMA COLLECTIONS ====================

/**
 * Collection of all broker-related schemas for easy access
 */
export const BrokerSchemas = {
  // Core schemas
  Broker: BrokerSchema,
  CreateBroker: CreateBrokerSchema,
  UpdateBroker: UpdateBrokerSchema,
  BrokerSearch: BrokerSearchSchema,

  // Response schemas
  PublicBroker: PublicBrokerSchema,
  BrokerConnection: BrokerConnectionSchema,

  // Operation schemas
  ValidateBrokerCredentials: ValidateBrokerCredentialsSchema,
  BrokerAuthResult: BrokerAuthResultSchema,

  // Component schemas
  BrokerSettings: brokerSettingsSchema,
  ApiKey: apiKeySchema,
  ApiSecret: apiSecretSchema,
  AccessToken: accessTokenSchema,
} as const;

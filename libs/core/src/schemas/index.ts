/**
 * QuestDB Schema Exports
 *
 * Centralized exports for QuestDB table schemas and management utilities.
 * This provides a unified interface for QuestDB schema operations across
 * the PatternTrade API ecosystem using the new repository pattern.
 *
 * Features:
 * - Automatic table registration and initialization
 * - Schema validation and verification
 * - Query templates and utilities
 * - Integration with BaseQuestDBRepository pattern
 *
 * Note: Table schemas are now managed by feature modules that register
 * their schemas with the QuestDB service during module initialization.
 */

// Legacy Symbol Schema (for backward compatibility)
export {
  SYMBOL_TABLE,
  CREATE_SYMBOL_TABLE_SQL,
  CREATE_SYMBOL_INDEXES_SQL,
  SymbolQuestDBTable,
  SymbolQueries,
} from './symbol.model';

// Re-export QuestDB repository types from common library
export type { QuestDBTableSchema } from '@app/common/questdb';

/**
 * @deprecated Use QuestDBTableSchema from @app/common/questdb instead
 */
export interface QuestDBSchemaManager {
  initialize(): Promise<void>;
  verifyTableStructure(): Promise<{
    tableExists: boolean;
    columnCount: number;
    indexCount: number;
    issues: string[];
  }>;
  getTableStats(): Promise<Record<string, unknown>>;
  cleanupOldData?(retentionDays: number): Promise<{
    deletedRecords: number;
    cutoffDate: Date;
  }>;
}

/**
 * @deprecated Registry of QuestDB schemas - use feature module schema registration instead
 *
 * Schemas are now automatically registered by feature modules during initialization.
 * Each feature module should register its schema with the QuestDB service using:
 * questdbService.registerTableSchema(schema)
 */
export const QUESTDB_SCHEMAS = {
  SYMBOL: {
    tableName: SYMBOL_TABLE,
    createTableSQL: CREATE_SYMBOL_TABLE_SQL,
    createIndexesSQL: CREATE_SYMBOL_INDEXES_SQL,
    queries: SymbolQueries,
  },
} as const;

/**
 * @deprecated Schema initialization utility - tables are now auto-initialized
 *
 * Table initialization is now handled automatically by the QuestDB service
 * when feature modules register their schemas during module initialization.
 */
export async function initializeAllQuestDBSchemas(questdbService: any): Promise<void> {
  console.warn('initializeAllQuestDBSchemas is deprecated. Tables are now auto-initialized by QuestDB service.');

  // For backward compatibility, still initialize the symbol table
  const symbolTable = new SymbolQuestDBTable(questdbService);
  await symbolTable.initialize();
}

/**
 * @deprecated Schema verification utility - use QuestDB service methods instead
 *
 * Schema verification is now handled by individual repository implementations
 * and the QuestDB service's table management methods.
 */
export async function verifyAllQuestDBSchemas(questdbService: any): Promise<{
  [key: string]: {
    tableExists: boolean;
    columnCount: number;
    indexCount: number;
    issues: string[];
  };
}> {
  console.warn('verifyAllQuestDBSchemas is deprecated. Use QuestDB service methods for schema verification.');

  const results: Record<string, any> = {};

  // For backward compatibility, still verify the symbol table
  const symbolTable = new SymbolQuestDBTable(questdbService);
  results.symbol = await symbolTable.verifyTableStructure();

  return results;
}

/**
 * Utility function to get all registered table schemas from QuestDB service
 *
 * @param questdbService - QuestDB service instance
 * @returns Map of registered table schemas
 */
export function getRegisteredTableSchemas(questdbService: any): Map<string, any> {
  if (typeof questdbService.getRegisteredTableSchemas === 'function') {
    return questdbService.getRegisteredTableSchemas();
  }

  console.warn('QuestDB service does not support getRegisteredTableSchemas method');
  return new Map();
}

/**
 * Utility function to check if tables have been initialized
 *
 * @param questdbService - QuestDB service instance
 * @returns Whether tables have been initialized
 */
export function areTablesInitialized(questdbService: any): boolean {
  if (typeof questdbService.areTablesInitialized === 'function') {
    return questdbService.areTablesInitialized();
  }

  console.warn('QuestDB service does not support areTablesInitialized method');
  return false;
}

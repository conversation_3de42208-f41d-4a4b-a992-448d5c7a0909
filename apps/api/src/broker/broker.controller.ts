import {
  <PERSON>,
  Get,
  Post,
  Body,
  Param,
  Patch,
  Delete,
  Query,
  Logger,
  UseGuards,
  ParseIntPipe,
} from '@nestjs/common';
import { createZodDto } from 'nestjs-zod';
import { AuthGuard, RolesGuard, Roles } from '@app/auth';
import { PaginationOptions, PaginatedResult } from '@app/common/repository';
import { BrokerService } from '@app/broker';
import { CreateBrokerSchema, UpdateBrokerSchema, BrokerSearchSchema, PublicBroker } from '@app/broker/broker.schema';

// ==================== DTO CLASSES ====================

/**
 * DTO for creating a new broker configuration
 */
class CreateBrokerDto extends createZodDto(CreateBrokerSchema) {}

/**
 * DTO for updating an existing broker configuration
 */
class UpdateBrokerDto extends createZodDto(UpdateBrokerSchema) {}

/**
 * DTO for searching and filtering broker configurations
 */
class BrokerSearchDto extends createZodDto(BrokerSearchSchema) {}

// ==================== CONTROLLER ====================

/**
 * Broker Controller for API Gateway
 *
 * Provides REST API endpoints for broker credential management operations.
 * Handles broker configuration CRUD operations with proper authentication,
 * authorization, validation, and error handling.
 *
 * Features:
 * - Broker credential management (create, read, update, delete)
 * - User-specific broker configurations
 * - Comprehensive search and filtering
 * - Pagination support for large datasets
 * - Role-based access control
 * - Input validation with Zod schemas
 * - Structured logging and error handling
 */
@Controller('brokers')
@UseGuards(AuthGuard, RolesGuard)
export class BrokerController {
  private readonly logger = new Logger(BrokerController.name);

  constructor(private readonly brokerService: BrokerService) {}

  // ==================== CRUD OPERATIONS ====================

  /**
   * Create a new broker configuration
   * Requirements: Admin role required for broker management
   */
  @Post()
  @Roles('admin')
  async createBroker(@Body() dto: CreateBrokerDto): Promise<PublicBroker> {
    this.logger.log(`Creating broker configuration: ${dto.name} (${dto.type}) for user ${dto.userId}`);

    const broker = await this.brokerService.createBroker(dto);

    this.logger.log(`Successfully created broker with ID: ${broker.id}`);
    return broker;
  }

  /**
   * Get all broker configurations with filtering and pagination
   * Requirements: Admin role required to view all brokers
   */
  @Get()
  @Roles('admin')
  async findAllBrokers(
    @Query() filters: BrokerSearchDto,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('offset', new ParseIntPipe({ optional: true })) offset?: number,
    @Query('sortBy') sortBy?: string,
    @Query('sortOrder') sortOrder?: 'ASC' | 'DESC',
  ): Promise<PaginatedResult<PublicBroker>> {
    this.logger.log('Finding brokers with filters', { filters, limit, offset, sortBy, sortOrder });

    const paginationOptions: PaginationOptions = {
      limit: limit || 10,
      offset: offset || 0,
      sortBy: sortBy || 'createdAt',
      sortOrder: sortOrder || 'DESC',
      filters: filters as Record<string, unknown>,
    };

    const result = await this.brokerService.findAllBrokers(filters, paginationOptions);

    this.logger.log(`Found ${result.data.length} brokers (${result.total} total)`);
    return result;
  }

  /**
   * Get broker configuration by ID
   * Requirements: Admin and user roles can view broker details
   */
  @Get(':id')
  @Roles('admin', 'user')
  async findBrokerById(@Param('id', ParseIntPipe) id: number): Promise<PublicBroker> {
    this.logger.log(`Finding broker with ID: ${id}`);

    // Note: In a real implementation, we would get the current user ID from the request context
    // and ensure users can only access their own brokers
    const userId = 'current-user-id'; // This should come from the authenticated user context
    const broker = await this.brokerService.findBrokerById(id, userId);

    this.logger.log(`Successfully found broker with ID: ${id}`);
    return broker;
  }

  /**
   * Update broker configuration
   * Requirements: Admin role required for broker management
   */
  @Patch(':id')
  @Roles('admin')
  async updateBroker(@Param('id', ParseIntPipe) id: number, @Body() dto: UpdateBrokerDto): Promise<PublicBroker> {
    this.logger.log(`Updating broker configuration ${id}`, { hasApiKey: !!dto.apiKey });

    // Note: In a real implementation, we would get the current user ID from the request context
    const userId = 'current-user-id'; // This should come from the authenticated user context
    const broker = await this.brokerService.updateBroker(id, userId, dto);

    this.logger.log(`Successfully updated broker with ID: ${id}`);
    return broker;
  }

  /**
   * Delete broker configuration
   * Requirements: Admin role required for broker management
   */
  @Delete(':id')
  @Roles('admin')
  async deleteBroker(@Param('id', ParseIntPipe) id: number): Promise<{ message: string }> {
    this.logger.log(`Deleting broker configuration ${id}`);

    // Note: In a real implementation, we would get the current user ID from the request context
    const userId = 'current-user-id'; // This should come from the authenticated user context
    const result = await this.brokerService.deleteBroker(id, userId);

    this.logger.log(`Successfully deleted broker with ID: ${id}`);
    return result;
  }

  // ==================== SPECIALIZED ENDPOINTS ====================

  /**
   * Get active broker configurations for a user
   * Requirements: Admin and user roles can view active brokers
   */
  @Get('active/user/:userId')
  @Roles('admin', 'user')
  async getActiveBrokers(
    @Param('userId') userId: string,
    @Query('limit', new ParseIntPipe({ optional: true })) limit?: number,
    @Query('offset', new ParseIntPipe({ optional: true })) offset?: number,
  ): Promise<PaginatedResult<PublicBroker>> {
    this.logger.log(`Getting active brokers for user: ${userId}`);

    const paginationOptions: PaginationOptions = {
      limit: limit || 10,
      offset: offset || 0,
      sortBy: 'lastConnectedAt',
      sortOrder: 'DESC',
    };

    const result = await this.brokerService.getActiveBrokers(userId, paginationOptions);

    this.logger.log(`Found ${result.data.length} active brokers for user ${userId}`);
    return result;
  }

  /**
   * Get broker status counts for a user
   * Requirements: Admin and user roles can view broker statistics
   */
  @Get('status/counts/:userId')
  @Roles('admin', 'user')
  async getBrokerStatusCounts(@Param('userId') userId: string): Promise<Record<string, number>> {
    this.logger.log(`Getting broker status counts for user: ${userId}`);

    const counts = await this.brokerService.getBrokerStatusCounts(userId);

    this.logger.log(`Retrieved broker status counts for user ${userId}`, counts);
    return counts;
  }

  /**
   * Update broker credentials
   * Requirements: Admin role required for credential management
   */
  @Patch(':id/credentials')
  @Roles('admin')
  async updateCredentials(@Param('id', ParseIntPipe) id: number, @Body() dto: any): Promise<PublicBroker> {
    this.logger.log(`Updating credentials for broker ${id}`);

    // Note: In a real implementation, we would get the current user ID from the request context
    const userId = 'current-user-id'; // This should come from the authenticated user context
    const broker = await this.brokerService.updateCredentials(id, userId, dto);

    this.logger.log(`Successfully updated credentials for broker ${id}`);
    return broker;
  }
}
